## 接口清单

| **接口名称** | **接口地址** |
| --- | --- |
| 通用大模型问答 | /api/acbackend/openchat/v1/chat/completions |
| 知识库列表 | /api/smartassistbackend/aiknowledge-open/v1/kb-list |
| 知识库检索 | /api/smartassistbackend/aiknowledge-open/v1/doc-search |
| 知识库问答 | /api/smartassistbackend/chat/open\_universal\_chat |
| 获取夜间模型列表 | /api/ aebackend/openmodel/v1/models |

## 接口详情

## 1.通用大模型问答

### 1.1 接口请求

<table><tbody><tr><td colspan="1" rowspan="1" width="146"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="308"><p><strong>说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="146"><p><span>传输方式</span></p></td><td colspan="1" rowspan="1" width="308"><p><span>https</span></p></td></tr><tr><td colspan="1" rowspan="1" width="146"><p><span>请求地址</span></p></td><td colspan="1" rowspan="1" width="308"><p><span>/api/acbackend/openchat/v1/chat/completions</span></p></td></tr><tr><td colspan="1" rowspan="1" width="146"><p><span>调用方式</span></p></td><td colspan="1" rowspan="1" width="308"><p><span>同步调用，等待模型执行完成并返回最终结果或 SSE 调用</span></p></td></tr><tr><td colspan="1" rowspan="1" width="146"><p><span>字符编码</span></p></td><td colspan="1" rowspan="1" width="308"><p><span>UTF-8</span></p></td></tr><tr><td colspan="1" rowspan="1" width="146"><p><span>接口请求格式</span></p></td><td colspan="1" rowspan="1" width="308"><p><span>JSON</span></p></td></tr><tr><td colspan="1" rowspan="1" width="146"><p><span>响应格式</span></p></td><td colspan="1" rowspan="1" width="308"><p><span>JSON 或标准 Stream Event</span></p></td></tr><tr><td colspan="1" rowspan="1" width="146"><p><span>接口请求类型</span></p></td><td colspan="1" rowspan="1" width="308"><p><span>POST</span></p></td></tr><tr><td colspan="1" rowspan="1" width="146"><p><span>开发语言</span></p></td><td colspan="1" rowspan="1" width="308"><p><span>任意可发起 http 请求的开发语言</span></p></td></tr></tbody></table>

### 1.2 请求参数

#### header请求参数

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| account | string | 是 | 研发云开放能力调用者id |
| time-stamp | string | 是 | 时间戳 |
| authorization | string | 是 | 认证信息 |
| apiKey | string | 是 | 当前用户的apiKey信息 |
| businessKey | string | 否 | 业务系统的key |

#### body请求参数

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| messages | List<Object> | 是 | 对话上下文的数组，每条消息包含角色（user，assistant）和内容，用于传递对话历史和控制模型行为。可能的Object类型包括： UserMessage (object) 、AssistantMessage (object) |
| frequency\_penalty | Float | 否 | 控制模型的输出风格和内容多样性，提升生成文本的质量和适用性。正值减少重复，负值增加重复，取值范围 -2.0 至 2.0，默认值：0 |
| logit\_bias | Object | 否 | 用于调整特定Token生成概率的参数。键为Token ID（整数），值为偏差值（浮点数）正偏差值增加Token出现的可能性，负偏差值减少。它可以控制内容、风格及敏感词过滤，偏差范围为 -100 至 100。 |
| logprobs | Boolean | 否 | 是否返回生成Token的对数概率，用于分析模型选择过程和输出的透明性。默认值：false |
| top\_logprobs | Interger | 否 | 返回生成Token的前N个备选Token及其对数概率，帮助理解模型选择和调试生成过程。logprobs为true时生效。取值范围：\[0,20\] |
| max\_tokens | Interger | 否 | 控制生成文本的最大Token数，此处指输出token，范围为1到模型支持的最大数目。 |
| n | Interger | 否 | 指定生成的独立响应数量，用于获取多个不同的回复，提升选择多样性，范围从1开始。默认值：1 |
| presence\_penalty | Float | 否 | 调整模型生成新话题的倾向，正值增加新话题的生成，负值减少，范围从 -2.0 到 2.0，默认值：0 |
| response\_format | Object | 否 | 指定API响应格式，根据需要选择结构化数据或纯文本返回。 |
| seed | Interger | 否 | 参数设置生成过程的随机数种子，用于确保相同输入生成一致的输出，适用于测试和结果重现。无法保证确定性，可参考 system\_fingerprint 响应参数来监视后端的变化。 |
| stop | List<String> | 否 | 指定一个或多个字符串，生成文本中出现这些字符串时停止输出，用于控制文本长度和内容。 |
| stream | Boolean | 否 | 控制是否以流式传输的方式返回文本，true 表示逐步发送生成内容，适合实时应用，此时将返回消息增量，就像在 ChatGPT 中一样。tokens将在可用时作为data-only server-sent events 返回，流以data: \[DONE\] 消息终止。默认值：false |
| temperature | Float | 否 | 控制生成文本的随机性，高值增加随机性和创意，低值提高确定性和一致性。取值范围：\[0,2\]，默认值：1 |
| top\_p | Float | 否 | 控制生成词的累积概率阈值，高值增加多样性，低值集中输出，与 temperature 结合优化生成策略。取值范围：\[0,1\]，默认值：1 |
| night\_model\_name | String | 否 | 夜间模式时选择的模型名称 |

### 1.3 响应参数

基于请求体中的stream字段返回不同类型的数据，stream=true时返回流式响应，否则返回非流式响应。

#### 流式响应

<table><tbody><tr><td colspan="1" rowspan="1" width="127"><p align="left"><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="111"><p align="left"><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="85"><p align="left"><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="293"><p align="left"><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>id</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>生成请求的唯一标识符。用于跟踪和引用特定的生成请求。相同请求的不同分片id一致。</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>choices</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>List&lt;Object&gt;</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>模型生成的多个候选响应，如果请求参数n&gt;1时可能返回多个，请求的最后一个分片该字段为空。Object类型为Choice</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>created</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>Integer</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>问答完成时的 Unix 时间戳（以秒为单位）。同一个请求的分片相同</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>object</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>对象类型，值为chat.completion.chunk</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>usage</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>Object</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>否</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>问答的使用统计信息，请求的最后一个分片有值，其他分片为null</span></p></td></tr></tbody></table>

#### 非流式响应

<table><tbody><tr><td colspan="1" rowspan="1" width="127"><p align="left"><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="111"><p align="left"><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="85"><p align="left"><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="293"><p align="left"><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>id</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>生成请求的唯一标识符。用于跟踪和引用特定的生成请求。</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>choices</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>List&lt;Object&gt;</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>模型生成的多个候选响应，如果请求参数n&gt;1时可能返回多个。</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>created</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>Integer</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>问答完成时的 Unix 时间戳（以秒为单位）。</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>object</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>对象类型，值为chat.completion</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>usage</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>Object</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>否</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>问答的使用统计信息</span></p></td></tr></tbody></table>

### 1.4 参数格式

#### messages的UserMessage

<table><tbody><tr><td colspan="1" rowspan="1" width="127"><p align="left"><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="111"><p align="left"><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="85"><p align="left"><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="293"><p align="left"><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>content</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>消息内容</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>role</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>值为user</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>name</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>否</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>标识特定对话者的名称，用于区分多角色对话中的发言者和个性化回复</span></p></td></tr></tbody></table>

#### messages的AssistantMessage

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| content | String | 是 | 消息内容 |
| role | String | 是 | 值为 assistant |
| name | String | 否 | 标识特定对话者的名称，用于区分多角色对话中的发言者和个性化回复 |
| reasoning\_content | string | 否 | 思考内容 |
| tool\_calls | List<Object> | 否 | 记录模型调用外部工具的过程，包括工具名称、参数和结果，支持复杂任务和信息获取。Object类型为 ToolCall |

#### ToolCall

<table><tbody><tr><td colspan="1" rowspan="1" width="127"><p align="left"><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="111"><p align="left"><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="85"><p align="left"><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="293"><p align="left"><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>id</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>调用的工具ID</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>type</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>工具的类型，目前只支持function</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>function</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>Object</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>模型调用的function，Object类型为Function</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>index</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>否</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>流式返回消息体中有值</span></p></td></tr></tbody></table>

#### function

<table><tbody><tr><td colspan="1" rowspan="1" width="127"><p align="left"><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="111"><p align="left"><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="85"><p align="left"><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="293"><p align="left"><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>name</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>function名称</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>arguments</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>调用函数时使用的参数，由模型以 JSON 格式生成。请注意，模型并不总是生成有效的 JSON，并且可能会产生函数架构未定义的参数。在调用函数之前，请验证代码中的参数。</span></p></td></tr></tbody></table>

#### response\_format

<table><tbody><tr><td colspan="1" rowspan="1" width="127"><p align="left"><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="111"><p align="left"><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="85"><p align="left"><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="293"><p align="left"><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>type</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>取值范围：text、json_object，默认值：text。设置为 { "type": "json_object" } 可启用 JSON 模式，从而保证模型生成的消息是有效的 JSON。重要提示：使用 JSON 模式时，您还必须通过系统或用户消息指示模型自行生成 JSON。如果没有这个，模型可能会生成无休止的空白流，直到生成达到token限制，从而导致长时间运行且看似“卡住”的请求。另请注意，如果 finish_reason="length"，则消息内容可能会被部分截断，这表明生成超过了 max_tokens 或对话超过了最大上下文长度。</span></p></td></tr></tbody></table>

#### Choice

<table><tbody><tr><td colspan="1" rowspan="1" width="127"><p align="left"><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="111"><p align="left"><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="85"><p align="left"><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="293"><p align="left"><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>finish_reason</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>取值范围：</span> <span>stop、length、tool_calls、content_filter。</span> <span>模型停止生成token的原因。如果模型达到自然停止点或提供的停止序列，则为 stop；如果达到请求中指定的最大标记数，则为 length；如果由于内容过滤器中的标志而省略内容，则为 content_filter；如果模型调用了工具，则为 tool_calls</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>index</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>Integer</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>生成结果中的索引</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>delta</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>Object</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>模型生成的消息</span></p></td></tr></tbody></table>

#### delta

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| content | String | 是 | 消息分片内容 |
| reasoning\_content | string | 否 | 思考内容 |
| tool\_calls | List<Object> | 是 | 工具调用的信息。Object类型为 ToolCall |
| role | String | 是 | 消息角色 |

#### usage

<table><tbody><tr><td colspan="1" rowspan="1" width="127"><p align="left"><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="111"><p align="left"><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="85"><p align="left"><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="293"><p align="left"><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>completion_tokens</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>Integer</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>模型生成用的token数</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>prompt_tokens</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>Integer</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>请求提示的token数</span></p></td></tr><tr><td colspan="1" rowspan="1" width="127"><p><span>total_tokens</span></p></td><td colspan="1" rowspan="1" width="111"><p><span>Integer</span></p></td><td colspan="1" rowspan="1" width="85"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="293"><p><span>总token数</span></p></td></tr></tbody></table>

### 1.5 调用示例

#### 非流式调用

##### 请求示例

```java
# API endpoint
url = "https://www.srdcloud.cn/api/acbackend/openchat/v1/chat/completions"

# Headers
headers = {
    "account": "your_srdcloud_account_here",
    "time-stamp": "your_srdcloud_time-stamp_here",
    "authorization": "your_srdcloud_authorization_here",
    "apiKey": "your_srdcloud_apiKey_here"
}

# request Body
request = {
    "messages": [
        {
            "content": "python和java哪个好用点",
            "role": "user"
        }
    ],
    "frequency_penalty": 0,
    "max_tokens": 1000,
    "n": 1,
    "presence_penalty": 0,
    "response_format": {
    "type": "text"
    },
    "seed": 0,
    "stop": [
    "string"
    ],
    "stream": False,
    "temperature": 1,
    "top_p": 1
}

# Send the POST request
response = requests.post(url, headers=headers, json=request)

# Check if the request was successful
if response.status_code == 200:
    print(response.text)
else:
    print(f"Request failed with status code: {response.status_code}")
    print("Response content:", response.content)
```

##### 响应示例

```java
{
  "id": "cmpl-9bf8c0a19e0246b0a6b27a4ed8fc3cc9",
  "object": "chat.completion",
  "created": 1728719067,
  "choices": [
    {
      "index": 0,
      "message": {
        "role": "assistant",
        "content": "Python和Java是两种非常不同的编程语言，它们各有优势和适用场景，所以无法简单地说哪个“更好”，而是要根据具体需求来选择。\n\n1. **Python**:\n   - **易于学习和使用**: Python的语法简单明了，减少了编码错误，也被作为许多新手的入门语言。\n   - **数据科学和机器学习**: Python在数据科学、数据分析、AI和机器学习项目中的应用广泛，有丰富的库如NumPy、Pandas、Matplotlib、Scikit-learn等。\n   - **动态类型**: 变量不需要提前声明类型，让编写代码更加灵活，但也会增加运行时错误的可能性。\n\n2. **Java**:\n   - **面向对象的编程语言**: Java是有严格的语法规范和静态类型的面向对象编程语言，适合开发大型项目。\n   - **平台独立性**: 通过Java虚拟机（JVM），Java应用程序可以在任何操作系统上运行，提高了可移植性。\n   - **企业级应用**: Java在企业级应用、大型系统集成、web应用开发、移动开发（尤其是Android）等方面非常强大。\n\n选择哪个语言取决于你的项目需求、团队技能、生态系统支持等因素。对于快速原型设计、数据分析或机器学习，Python可能是更好的选择；而对于企业级应用、稳定性和性能要求较高的项目，Java可能更合适。两者都有丰富的社区支持和文档资源，选择最适合项目和你的语言即可。"
      },
      "finish_reason": "stop"
    }
  ],
  "usage": {
    "prompt_tokens": 301,
    "completion_tokens": 296,
    "total_tokens": 597
  }
}
```

#### 流式调用

##### 请求示例

```java
# API endpoint
url = "https://www.srdcloud.cn/api/acbackend/openchat/v1/chat/completions"

# Headers
headers = {
    "account": "your_srdcloud_account_here",
    "time-stamp": "your_srdcloud_time-stamp_here",
    "authorization": "your_srdcloud_authorization_here",
    "apiKey": "your_srdcloud_apiKey_here"
}

# request Body
request = {
    "messages": [
        {
            "content": "python和java哪个好用点",
            "role": "user"
        }
    ],
    "frequency_penalty": 0,
    "max_tokens": 1000,
    "n": 1,
    "presence_penalty": 0,
    "response_format": {
    "type": "text"
    },
    "seed": 0,
    "stop": [
    "string"
    ],
    "stream": False,
    "temperature": 1,
    "top_p": 1
}

# Send the POST request
response = requests.post(url, headers=headers, json=request)

# Check if the request was successful
if response.status_code == 200:
    for line in response.iter_lines():
        if line:
            print(line.decode("utf-8"))
else:
    print(f"Request failed with status code: {response.status_code}")
    print("Response content:", response.content)
```

##### 响应示例

```java
data: {"id":"chat-6fbd7552f18c43848d8cc6acbe1c46a4","object":"chat.completion.chunk","created":1728722178,"choices":[{"index":0,"delta":{"role":"assistant"},"finish_reason":""}]}
data: {"id":"chat-6fbd7552f18c43848d8cc6acbe1c46a4","object":"chat.completion.chunk","created":1728722178,"choices":[{"index":0,"delta":{"content":"P"},"finish_reason":""}]}
data: {"id":"chat-6fbd7552f18c43848d8cc6acbe1c46a4","object":"chat.completion.chunk","created":1728722178,"choices":[{"index":0,"delta":{"content":"y"},"finish_reason":""}]}
......
data: {"id":"chat-6fbd7552f18c43848d8cc6acbe1c46a4","object":"chat.completion.chunk","created":1728722178,"choices":[{"index":0,"delta":{"content":"和你"},"finish_reason":""}]}
data: {"id":"chat-6fbd7552f18c43848d8cc6acbe1c46a4","object":"chat.completion.chunk","created":1728722178,"choices":[{"index":0,"delta":{"content":"的"},"finish_reason":""}]}
data: {"id":"chat-6fbd7552f18c43848d8cc6acbe1c46a4","object":"chat.completion.chunk","created":1728722178,"choices":[{"index":0,"delta":{"content":"语言即可。"},"finish_reason":"stop"}]}
data: [DONE]
```

## 2\. 知识库列表

### 2.1 接口请求

<table><tbody><tr><td colspan="1" rowspan="1" width="162"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="346.953"><p><strong>说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>传输方式</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>https</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>请求地址</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>/api/smartassistbackend/aiknowledge-open/v1/kb-list</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>字符编码</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>UTF-8</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>接口请求格式</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>application/json</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>响应格式</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>application/json</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>接口请求类型</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>Get</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>开发语言</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>任意可发起 http 请求的开发语言</span></p></td></tr></tbody></table>

### 2.2 请求参数

#### header请求参数

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| account | string | 是 | 研发云开放能力调用者id |
| time-stamp | string | 是 | 时间戳 |
| authorization | string | 是 | 认证信息 |
| apiKey | string | 是 | 当前用户的apiKey信息 |
| businessKey | string | 否 | 业务系统key |

#### 响应参数

<table><tbody><tr><td colspan="1" rowspan="1" width="133"><p><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="115"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="90"><p><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="278"><p><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="133"><p><span>optResult</span></p></td><td colspan="1" rowspan="1" width="115"><p><span>int</span></p></td><td colspan="1" rowspan="1" width="90"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="278"><p><span>操作结果，0成功，其他失败</span></p></td></tr><tr><td colspan="1" rowspan="1" width="133"><p><span>msg</span></p></td><td colspan="1" rowspan="1" width="115"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="90"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="278"><p><span>说明</span></p></td></tr><tr><td colspan="1" rowspan="1" width="133"><p><span>data</span></p></td><td colspan="1" rowspan="1" width="115"><p><span>List&lt;</span> <span>KbInfo</span></p><p><span>&gt;</span></p></td><td colspan="1" rowspan="1" width="90"><p><span>否</span></p></td><td colspan="1" rowspan="1" width="278"><p><span>列表结果内容</span></p></td></tr></tbody></table>

##### KbInfo:

<table><tbody><tr><td colspan="1" rowspan="1" width="130"><p><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="130"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="130"><p><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="226"><p><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>kbId</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>int</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>知识库id</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>kbName</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>知识库名称</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>openFlag</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>bool</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>是否为公开知识库，true为已公开</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>type</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>知识库类型：通用文档/代码文件</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>kbLevel</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>int</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>知识库种类，1个人，2项目，3组织</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>createUser</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>创建人，个人知识库时有值</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>organization</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>组织名称,多级以逗号分隔, 组织知识库时有值</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>projectId</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>int</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>项目Id， 项目知识库时有值</span></p></td></tr></tbody></table>

### 2.3 调用示例

#### 请求示例

```java
curl -X GET -H  "Accept:*/*" -H  "Content-Type:application/json"  -H "account: your_srdcloud_account_here" -H "time-stamp: your_srdcloud_time-stamp_here" -H "authorization: your_srdcloud_authorization_here" -H   "apikey: your_srdcloud_apiKey_here" "https://www.srdcloud.cn/api/smartassistbackend/aiknowledge-open/v1/kb-list"
```

#### 响应示例

```java
{
  "data": [{
    "createUser": "张三",
    "kbId": 11,
    "kbLevel": 1,
    "kbName": "我的知识库1",
    "openFlag": false,
    "type": "代码文件"
  }, {
    "kbId": 22,
    "kbLevel": 2,
    "kbName": "我的项目知识库1",
    "openFlag": false,
    "projectId": 20137,
    "type": "通用文档"
  }, {
    "kbId": 33,
    "kbLevel": 3,
    "kbName": "组织知识库1",
    "organization": "一级组织，二级组织"
    "openFlag": false,
    "type": "代码文件"
  }],
  "msg": "成功",
  "optResult": 0
}
```

## 3\. 知识库检索

### 3.1 接口请求

<table><tbody><tr><td colspan="1" rowspan="1" width="162"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="346.953"><p><strong>说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>传输方式</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>https</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>请求地址</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>/api/smartassistbackend/aiknowledge-open/v1/doc-search</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>字符编码</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>UTF-8</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>接口请求格式</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>application/json</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>响应格式</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>application/json</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>接口请求类型</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>POST</span></p></td></tr><tr><td colspan="1" rowspan="1" width="162"><p><span>开发语言</span></p></td><td colspan="1" rowspan="1" width="346.953"><p><span>任意可发起 http 请求的开发语言</span></p></td></tr></tbody></table>

### 3.2 请求参数

#### header请求参数

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| account | string | 是 | 研发云开放能力调用者id |
| time-stamp | string | 是 | 时间戳 |
| authorization | string | 是 | 认证信息 |
| apiKey | string | 是 | 当前用户的apiKey信息 |
| businessKey | string | 否 | 业务系统key |

#### body请求参数

<table><tbody><tr><td colspan="1" rowspan="1" width="133"><p><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="113"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="90"><p><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="278"><p><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="133"><p><span>kbId</span></p></td><td colspan="1" rowspan="1" width="113"><p><span>int</span></p></td><td colspan="1" rowspan="1" width="90"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="278"><p><span>知识库id</span></p></td></tr><tr><td colspan="1" rowspan="1" width="133"><p><span>doc</span></p></td><td colspan="1" rowspan="1" width="113"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="90"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="278"><p><span>检索内容</span></p></td></tr></tbody></table>

知识库检索中的其他相关参数，沿用 web 端知识库页面的配置值，若需调整，请前往 web 端知识库配置处更新。

### 3.3 响应参数

<table><tbody><tr><td colspan="1" rowspan="1" width="133"><p><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="115"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="90"><p><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="278"><p><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="133"><p><span>optResult</span></p></td><td colspan="1" rowspan="1" width="115"><p><span>int</span></p></td><td colspan="1" rowspan="1" width="90"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="278"><p><span>操作结果，0成功，其他失败</span></p></td></tr><tr><td colspan="1" rowspan="1" width="133"><p><span>msg</span></p></td><td colspan="1" rowspan="1" width="115"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="90"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="278"><p><span>说明</span></p></td></tr><tr><td colspan="1" rowspan="1" width="133"><p><span>data</span></p></td><td colspan="1" rowspan="1" width="115"><p><span>ServerResultRetrievalResultVo</span></p></td><td colspan="1" rowspan="1" width="90"><p><span>否</span></p></td><td colspan="1" rowspan="1" width="278"><p><span>检索结果内容</span></p></td></tr></tbody></table>

#### ServerResultRetrievalResultVo：

<table><tbody><tr><td colspan="1" rowspan="1" width="130"><p><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="130"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="130"><p><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="226"><p><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>costTime</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>int</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>总耗时(ms)</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>items</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>List&lt;RetrievalResultItem&gt;</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>检索结果列表</span></p></td></tr></tbody></table>

#### RetrievalResultItem:

<table><tbody><tr><td colspan="1" rowspan="1" width="130"><p><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="130"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="130"><p><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="226"><p><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>content</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>内容文本</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>fileName</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>文件名</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>fileId</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>int</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>文件ID</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>seqNum</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>int</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>切片序号</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>rerankScore</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>float</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>重排分数</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>rerankSeq</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>int</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>重排位次</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>distance</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>float</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>召回分数</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>distanceSeq</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>int</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>召回排位</span></p></td></tr><tr><td colspan="1" rowspan="1" width="130"><p><span>sliceId</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="130"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>切片分区ID</span></p></td></tr></tbody></table>

### 3.4 调用示例

#### 请求示例

```java
curl -X POST -H  "Accept:*/*" -H "Content-Type:application/json" -H "account: your_srdcloud_account_here" -H "time-stamp: your_srdcloud_time-stamp_here" -H "authorization: your_srdcloud_authorization_here" -H   "apikey: your_srdcloud_apiKey_here" "https://www.srdcloud.cn/api/smartassistbackend/aiknowledge-open/v1/doc-search" -d '{"doc":"测试","kbId":1}'
```

#### 响应示例

```java
{
  "data": {
    "costTime": 4440,
    "items": [{
      "content": "这是一段测试内容",
      "distance": 0.********,
      "distanceSeq": 1,
      "fileId": 4452,
      "fileName": "文档1.md",
      "seqNum": 3,
      "sliceId": "d11231c-b1bd-49ef-90ab-d23216eff0"
    }, {
      "content": "这是一段测试内容1",
      "distance": 0.********,
      "distanceSeq": 2,
      "fileId": 4452,
      "fileName": "文档2.md",
      "seqNum": 6,
      "sliceId": "6ds132-2bca-4f4d-ba4a-d312212b33"
    }]
  },
  "msg": "成功",
  "optResult": 0
}
```

## 4\. 知识库问答

### 4.1 接口请求

| **类型** | **说明** |
| --- | --- |
| 传输方式 | https |
| 请求地址 | /api/smartassistbackend/chat/open\_universal\_chat |
| 调用方式 | 同步、异步 SSE 调用 |
| 字符编码 | UTF-8 |
| 接口请求格式 | JSON |
| 响应格式 | JSON 或标准 Stream Event |
| 接口请求类型 | POST |
| 开发语言 | 任意可发起 http 请求的开发语言 |

### 4.2 请求参数

#### header请求参数

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| account | string | 是 | 研发云开放能力调用者 id |
| time-stamp | string | 是 | 时间戳 |
| authorization | string | 是 | 认证信息 |
| apiKey | string | 是 | 当前用户的 apiKey 信息 |
| businessKey | string | 否 | 业务系统key |

#### body请求参数

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| query | String | 是 | 研发云开放能力调用者 id |
| history | List\[Object\] | 否 | 会话历史记录 |
| knowledge\_base\_id | Int | 是 | 知识库 ID |
| model\_type | String | 否 | 模型类型，支持 chat 和 think |
| filter\_name | String | 否 | 防火墙类型，支持 basic 和 null |
| night\_model\_name | String | 否 | 模型名称，该字段在夜间时段生效，取值来源于 获取夜间模式能力开发模型列表接口 |

### 4.3 响应参数

#### 流式响应

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| from\_llm | Boolean | 是 | 是否来自大模型 |
| model\_name | String | 是 | 模型名称 |
| answer | String | 是 | 模型回答内容 |
| reason\_content | String | 是 | 模型思考内容 |
| is\_think | Boolean | 是 | 是否为思考内容 |
| finish\_reason | String | 是 | 结束符标识 |
| state\_code | Int | 是 | 状态码 |
| knowledge\_base\_files | List\[Object\] | 否 | 参考文献列表，最后一个分片携带 |
| sensitive\_word | String | 否 | 此字段用于标识或处理敏感信息，仅当片段中检测到敏感内容时，该字段才会被返回或填充 |

### 4.4 参数格式

#### History

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| role | String | 是 | 用户角色 |
| content | String | 是 | 聊天内容 |
| reqTime | String | 是 | 时间 |

#### knowledge\_base\_files

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| page\_content | string | 是 | 参考文献内容 |
| metadata | Object | 是 | 文献详细信息 |

#### metadata

| **参数名称** | **类型** | **是否必填** | **参数说明** |
| --- | --- | --- | --- |
| source | string | 是 | 参考文献来源 |
| seq\_num | int | 是 | 子片段序号 |
| slice\_id | string | 是 | 子片段 ID |
| slice\_type | string | 是 | 子片段类型 |
| full\_text | string | 是 | 用于 Qa 对 |
| knowledge\_base | string | 是 | 知识库名称 |
| knowledge\_base\_id | int | 是 | 知识库 ID |

### 4.5 调用示例

#### 流式调用

##### 请求示例

```python
# API endpoint
url = "https://www.srdcloud.cn/api/smartassistbackend/chat/open_universal_chat"
import requests, os, sys
inputdata = {
    "query": "软件测试是什么",
    "history": [
  {
    "role": "user",
    "content": "软件测试是什么",
    "reqTime": "2025-06-23 09:45:18"
  },
  {
    "content": "\n\n根据中国电信软件研发规范的定义......",
    "role": "assistant"
  },
  {
    "role": "user",
    "content": "你好",
    "reqTime": "2025-06-23 09:45:46"
  },
  {
    "content": "请问有什么需要帮忙的",
    "role": "assistant"
  }
],
    "knowledge_base_id": 568,
    "model_type": "think", #chat/think
    "filter_name": "null"  #null/basic
}
```
```python
# Headers
headers = {
    "account": "your_srdcloud_account_here",
    "time-stamp": "your_srdcloud_time-stamp_here",
    "authorization": "your_srdcloud_authorization_here",
    "apiKey": "your_srdcloud_apiKey_here"
}

# Send the POST request
response = requests.post(url, headers=headers, json=request)

# Check if the request was successful
if response.status_code == 200:
    for line in response.iter_lines():
        if line:
            print(line.decode("utf-8"))
else:
    print(f"Request failed with status code: {response.status_code}")
    print("Response content:", response.content)
```

##### 响应示例

```python
data: {"from_llm": true, "model_name": "Qwen3-32B", "answer": "类型测试", "reason_content": null, "is_think": false, "finish_reason": null, "state_code": 200}

data: {"from_llm": true, "model_name": "Qwen3-32B", "answer": "活动由项目的", "reason_content": null, "is_think": false, "finish_reason": null, "state_code": 200}

data: {"from_llm": true, "model_name": "Qwen3-32B", "answer": "测试人员", "reason_content": null, "is_think": false, "finish_reason": null, "state_code": 200}

......

data: {"from_llm": true, "model_name": "Qwen3-32B", "answer": "执行。", "reason_content": null, "is_think": false, "finish_reason": "stop", "state_code": 200, "knowledge_base_files": [{"page_content": "软件开发过程 > 测试：软件测试类型主要包括单元测试、集成测试……测试管理分册》。\n", "metadata": {"source": "/data/..../研发规范总体规范.docx", "seq_num": 31, "slice_id": "64ea2123-aaf3-4f30-9450-5708e10ae5c1", "slice_type": "parent", "full_text": "", "knowledge_base": "xxx知识库", "knowledge_base_id": 568}, "type": "Document"}, {"page_content": "文档说明 > 名词解释：……项目客户方项目经理及接口人等。", "metadata": {"source": "/data/..../研发规范总体规范.docx", "seq_num": 7, "slice_id": "9dc59d68-2b1a-468a-87ee-e46ad996b9de", "slice_type": "parent", "full_text": "", "knowledge_base": "zjh知识库", "knowledge_base_id": 568}, "type": "Document"}, {"page_content": "文档说明 > 名词解释：……件系统。", "metadata": {"source": "/data/..../研发规范总体规范.docx", "seq_num": 8, "slice_id": "e9d79983-5b6b-40d6-8134-3ca005e75272", "slice_type": "parent", "full_text": "", "knowledge_base": "xxx知识库", "knowledge_base_id": 568}, "type": "Document"}]}
```

## 5、获取夜间模型列表

### 5.1 接口请求

<table><tbody><tr><td colspan="1" rowspan="1" width="161.994"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="341.291"><p><strong>说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="161.994"><p><span>传输方式</span></p></td><td colspan="1" rowspan="1" width="341.291"><p><span>https</span></p></td></tr><tr><td colspan="1" rowspan="1" width="161.994"><p><span>请求地址</span></p></td><td colspan="1" rowspan="1" width="341.291"><p><span>/api/</span> <span>aebackend/openmodel/v1/models</span></p></td></tr><tr><td colspan="1" rowspan="1" width="161.994"><p><span>调用方式</span></p></td><td colspan="1" rowspan="1" width="341.291"><p><span>同步</span></p></td></tr><tr><td colspan="1" rowspan="1" width="161.994"><p><span>字符编码</span></p></td><td colspan="1" rowspan="1" width="341.291"><p><span>UTF-8</span></p></td></tr><tr><td colspan="1" rowspan="1" width="161.994"><p><span>接口请求格式</span></p></td><td colspan="1" rowspan="1" width="341.291"><p><span>JSON</span></p></td></tr><tr><td colspan="1" rowspan="1" width="161.994"><p><span>响应格式</span></p></td><td colspan="1" rowspan="1" width="341.291"><p><span>JSON</span></p></td></tr><tr><td colspan="1" rowspan="1" width="161.994"><p><span>接口请求类型</span></p></td><td colspan="1" rowspan="1" width="341.291"><p><span>GET</span></p></td></tr><tr><td colspan="1" rowspan="1" width="161.994"><p><span>开发语言</span></p></td><td colspan="1" rowspan="1" width="341.291"><p><span>任意可发起 http 请求的开发语言</span></p></td></tr></tbody></table>

### 5.2 请求参数

#### header请求参数

<table><tbody><tr><td colspan="1" rowspan="1" width="142.**************"><p><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="103.9972"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="101"><p><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="226"><p><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="142.**************"><p><span>account</span></p></td><td colspan="1" rowspan="1" width="103.9972"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="101"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>研发云开放能力调用者id</span></p></td></tr><tr><td colspan="1" rowspan="1" width="142.**************"><p><span>time-stamp</span></p></td><td colspan="1" rowspan="1" width="103.9972"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="101"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>时间戳</span></p></td></tr><tr><td colspan="1" rowspan="1" width="142.**************"><p><span>authorization</span></p></td><td colspan="1" rowspan="1" width="103.9972"><p><span>string</span></p></td><td colspan="1" rowspan="1" width="101"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="226"><p><span>认证信息</span></p></td></tr></tbody></table>

#### query请求参数

无

### 5.3 响应参数

<table><tbody><tr><td colspan="1" rowspan="1" width="128"><p><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="114"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="91"><p><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="281"><p><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="128"><p><span>optResult</span></p></td><td colspan="1" rowspan="1" width="114"><p><span>Integer</span></p></td><td colspan="1" rowspan="1" width="91"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="281"><p><span>请求结果。0 - 成功，其他 - 失败</span></p></td></tr><tr><td colspan="1" rowspan="1" width="128"><p><span>msg</span></p></td><td colspan="1" rowspan="1" width="114"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="91"><p><span>否</span></p></td><td colspan="1" rowspan="1" width="281"><p><span>请求失败时对应的错误信息</span></p></td></tr><tr><td colspan="1" rowspan="1" width="128"><p><span>models</span></p></td><td colspan="1" rowspan="1" width="114"><p><span>List[ModelInfo]</span></p></td><td colspan="1" rowspan="1" width="91"><p><span>否</span></p></td><td colspan="1" rowspan="1" width="281"><p><span>返回可用的模型列表</span></p></td></tr></tbody></table>

### 5.4 参数格式

#### ModelInfo

<table><tbody><tr><td colspan="1" rowspan="1" width="128"><p><strong>参数名称</strong></p></td><td colspan="1" rowspan="1" width="114"><p><strong>类型</strong></p></td><td colspan="1" rowspan="1" width="91"><p><strong>是否必填</strong></p></td><td colspan="1" rowspan="1" width="281"><p><strong>参数说明</strong></p></td></tr><tr><td colspan="1" rowspan="1" width="128"><p><span>modelName</span></p></td><td colspan="1" rowspan="1" width="114"><p><span>String</span></p></td><td colspan="1" rowspan="1" width="91"><p><span>是</span></p></td><td colspan="1" rowspan="1" width="281"><p><span>模型名称</span></p></td></tr></tbody></table>

### 5.5 调用示例

#### 请求示例

```python
# API endpoint
url = "https://www.srdcloud.cn/api/aebackend/openmodel/v1/models"

# Headers
headers = {
    "account": "your_srdcloud_account_here",
    "time-stamp": "your_srdcloud_time-stamp_here",
    "authorization": "your_srdcloud_authorization_here",
}

# Send the GET request
response = requests.get(url, headers=headers)

# Check if the request was successful
if response.status_code == 200:
    print(response.text)
else:
    print(f"Request failed with status code: {response.status_code}")
    print("Response content:", response.content)
```

#### 响应示例

```python
{
  "optResult": 0,
  "msg": "",
  "models": [
    {
      "modelName": "DeepSeek-V3-w8a8",
    }
  ]
}
```
