"""CodeFree to OpenAI API compatibility service."""

import asyncio
import json
import uuid
from typing import As<PERSON><PERSON><PERSON>ator, Dict, Any
from contextlib import asynccontextmanager

import structlog
from fastapi import FastAPI, HTTPException, Request
from fastapi.responses import StreamingResponse
from fastapi.middleware.cors import CORSMiddleware

from codefree.config import config
from codefree.models import (
    OpenAIChatCompletionRequest, OpenAIChatCompletionResponse,
    OpenAIModelsResponse, KnowledgeBaseChatRequest
)
from codefree.client import client
from codefree.converter import converter
from codefree.middleware import LoggingMiddleware, ErrorHandlingMiddleware, AuthenticationMiddleware
from codefree.exceptions import CodeFreeAPIError

# Configure logging
structlog.configure(
    processors=[
        structlog.stdlib.filter_by_level,
        structlog.stdlib.add_logger_name,
        structlog.stdlib.add_log_level,
        structlog.stdlib.PositionalArgumentsFormatter(),
        structlog.processors.TimeStamper(fmt="iso"),
        structlog.processors.StackInfoRenderer(),
        structlog.processors.format_exc_info,
        structlog.processors.UnicodeDecoder(),
        structlog.processors.JSONRenderer()
    ],
    context_class=dict,
    logger_factory=structlog.stdlib.LoggerFactory(),
    wrapper_class=structlog.stdlib.BoundLogger,
    cache_logger_on_first_use=True,
)

logger = structlog.get_logger()


@asynccontextmanager
async def lifespan(app: FastAPI):
    """Application lifespan manager."""
    logger.info("Starting CodeFree to OpenAI API service",
                host=config.HOST,
                port=config.PORT)
    yield
    logger.info("Shutting down CodeFree to OpenAI API service")


app = FastAPI(
    title="CodeFree to OpenAI API",
    description="OpenAI-compatible API for CodeFree services",
    version="1.0.0",
    lifespan=lifespan
)

# Add middleware
app.add_middleware(LoggingMiddleware)
app.add_middleware(ErrorHandlingMiddleware)
app.add_middleware(AuthenticationMiddleware, require_auth=False)  # Set to True to require API keys
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)


@app.get("/")
async def root():
    """Root endpoint."""
    return {
        "message": "CodeFree to OpenAI API compatibility service",
        "version": "1.0.0",
        "endpoints": {
            "chat_completions": "/v1/chat/completions",
            "models": "/v1/models",
            "knowledge_bases": "/v1/knowledge-bases",
            "knowledge_base_search": "/v1/knowledge-bases/{kb_id}/search",
            "knowledge_base_chat": "/v1/knowledge-bases/{kb_id}/chat"
        }
    }


@app.get("/health")
async def health_check():
    """Health check endpoint."""
    return {
        "status": "healthy",
        "service": "codefree-to-openai",
        "version": "1.0.0",
        "timestamp": int(time.time())
    }


@app.get("/v1/models")
async def list_models() -> OpenAIModelsResponse:
    """List available models."""
    try:
        models_response = await client.get_models()
        return converter.codefree_models_to_openai(models_response)
    except Exception as e:
        logger.error("Error listing models", error=str(e))
        raise HTTPException(status_code=500, detail=f"Error listing models: {str(e)}")


@app.post("/v1/chat/completions")
async def chat_completions(request: OpenAIChatCompletionRequest):
    """OpenAI-compatible chat completions endpoint."""
    try:
        # Convert OpenAI request to CodeFree format
        codefree_request = converter.openai_to_codefree_request(request)

        # Generate request ID
        request_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"

        if request.stream:
            # Stream response
            async def generate_stream():
                try:
                    async for chunk in client.chat_completions(codefree_request):
                        openai_chunk = converter.codefree_to_openai_stream_chunk(
                            chunk, request.model, request_id
                        )
                        yield f"data: {openai_chunk.json()}\n\n"

                    yield "data: [DONE]\n\n"

                except Exception as e:
                    logger.error("Error in stream generation", error=str(e))
                    error_chunk = {
                        "id": request_id,
                        "object": "chat.completion.chunk",
                        "created": int(asyncio.get_event_loop().time()),
                        "model": request.model,
                        "choices": [{
                            "index": 0,
                            "delta": {},
                            "finish_reason": "error"
                        }],
                        "error": str(e)
                    }
                    yield f"data: {json.dumps(error_chunk)}\n\n"
                    yield "data: [DONE]\n\n"

            return StreamingResponse(
                generate_stream(),
                media_type="text/plain",
                headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
            )
        else:
            # Non-stream response
            response_data = None
            async for chunk in client.chat_completions(codefree_request):
                response_data = chunk
                break  # Only get the first (and only) response for non-stream

            if not response_data:
                raise HTTPException(status_code=500, detail="No response from CodeFree API")

            openai_response = converter.codefree_to_openai_response(
                response_data, request.model, request_id
            )
            return openai_response

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error in chat completions", error=str(e))
        raise HTTPException(status_code=500, detail=f"Error in chat completions: {str(e)}")


@app.get("/v1/knowledge-bases")
async def list_knowledge_bases():
    """List available knowledge bases."""
    try:
        kb_response = await client.get_knowledge_bases()
        if kb_response.opt_result != 0:
            raise HTTPException(status_code=400, detail=kb_response.msg)

        return {
            "object": "list",
            "data": [
                {
                    "id": kb.kb_id,
                    "name": kb.kb_name,
                    "type": kb.type,
                    "level": kb.kb_level,
                    "open_flag": kb.open_flag,
                    "create_user": kb.create_user,
                    "organization": kb.organization,
                    "project_id": kb.project_id
                }
                for kb in (kb_response.data or [])
            ]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error listing knowledge bases", error=str(e))
        raise HTTPException(status_code=500, detail=f"Error listing knowledge bases: {str(e)}")


@app.post("/v1/knowledge-bases/{kb_id}/search")
async def search_knowledge_base(kb_id: int, request: Dict[str, Any]):
    """Search in a knowledge base."""
    try:
        query = request.get("query", "")
        if not query:
            raise HTTPException(status_code=400, detail="Query is required")

        search_response = await client.search_knowledge_base(kb_id, query)
        if search_response.opt_result != 0:
            raise HTTPException(status_code=400, detail=search_response.msg)

        return {
            "object": "search_result",
            "cost_time": search_response.data.cost_time if search_response.data else 0,
            "results": [
                {
                    "content": item.content,
                    "file_name": item.file_name,
                    "file_id": item.file_id,
                    "seq_num": item.seq_num,
                    "distance": item.distance,
                    "distance_seq": item.distance_seq,
                    "slice_id": item.slice_id,
                    "rerank_score": item.rerank_score,
                    "rerank_seq": item.rerank_seq
                }
                for item in (search_response.data.items if search_response.data else [])
            ]
        }
    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error searching knowledge base", kb_id=kb_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Error searching knowledge base: {str(e)}")


@app.post("/v1/knowledge-bases/{kb_id}/chat")
async def knowledge_base_chat(kb_id: int, request: Dict[str, Any]):
    """Chat with a knowledge base using OpenAI-compatible format."""
    try:
        # Extract query from messages (OpenAI format)
        messages = request.get("messages", [])
        if not messages:
            raise HTTPException(status_code=400, detail="Messages are required")

        # Get the last user message as query
        query = None
        history = []
        for msg in messages:
            if msg.get("role") == "user":
                query = msg.get("content", "")
            elif msg.get("role") == "assistant":
                history.append({
                    "role": "assistant",
                    "content": msg.get("content", "")
                })

        if not query:
            raise HTTPException(status_code=400, detail="No user message found")

        # Create knowledge base chat request
        kb_request = KnowledgeBaseChatRequest(
            query=query,
            history=history,
            knowledge_base_id=kb_id,
            model_type=request.get("model_type", "chat"),
            filter_name=request.get("filter_name", "null"),
            night_model_name=request.get("night_model_name")
        )

        # Generate request ID
        request_id = f"chatcmpl-{uuid.uuid4().hex[:29]}"
        model = request.get("model", "gpt-3.5-turbo")

        # Always stream for knowledge base chat
        async def generate_kb_stream():
            try:
                async for chunk in client.knowledge_base_chat(kb_request):
                    openai_chunk = converter.knowledge_base_chat_to_openai_stream(
                        chunk, model, request_id
                    )
                    yield f"data: {openai_chunk.json()}\n\n"

                yield "data: [DONE]\n\n"

            except Exception as e:
                logger.error("Error in KB stream generation", error=str(e))
                error_chunk = {
                    "id": request_id,
                    "object": "chat.completion.chunk",
                    "created": int(asyncio.get_event_loop().time()),
                    "model": model,
                    "choices": [{
                        "index": 0,
                        "delta": {},
                        "finish_reason": "error"
                    }],
                    "error": str(e)
                }
                yield f"data: {json.dumps(error_chunk)}\n\n"
                yield "data: [DONE]\n\n"

        return StreamingResponse(
            generate_kb_stream(),
            media_type="text/plain",
            headers={"Cache-Control": "no-cache", "Connection": "keep-alive"}
        )

    except HTTPException:
        raise
    except Exception as e:
        logger.error("Error in knowledge base chat", kb_id=kb_id, error=str(e))
        raise HTTPException(status_code=500, detail=f"Error in knowledge base chat: {str(e)}")


def main():
    """Main entry point."""
    import uvicorn
    uvicorn.run(
        "codefree.main:app",
        host=config.HOST,
        port=config.PORT,
        log_level=config.LOG_LEVEL.lower(),
        reload=False
    )


if __name__ == "__main__":
    main()
