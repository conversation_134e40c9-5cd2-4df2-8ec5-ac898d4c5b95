"""CodeFree API client."""

import time
import httpx
from typing import As<PERSON><PERSON><PERSON><PERSON>, Dict, Any, Optional
import json
import structlog

from codefree.config import config
from codefree.models import (
    CodeFreeChatRequest, CodeFreeChatResponse,
    KnowledgeBaseListResponse, KnowledgeBaseSearchResponse,
    KnowledgeBaseChatRequest, ModelsResponse
)

logger = structlog.get_logger()


class CodeFreeClient:
    """Client for CodeFree API."""

    def __init__(self):
        self.base_url = config.CODEFREE_BASE_URL
        self.timeout = httpx.Timeout(60.0, connect=10.0)

    def _get_headers(self) -> Dict[str, str]:
        """Get headers for CodeFree API requests."""
        return {
            "account": config.account,
            "time-stamp": str(int(time.time())),
            "authorization": config.authorization,
            "apiKey": config.api_key,
            "businessKey": config.business_key or "",
            "Content-Type": "application/json",
            "Accept": "*/*"
        }

    async def chat_completions(
        self,
        request: Code<PERSON>reeChatRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Send chat completion request to CodeFree API."""
        url = f"{self.base_url}/api/acbackend/openchat/v1/chat/completions"
        headers = self._get_headers()

        logger.info("Sending chat completion request", url=url, stream=request.stream)

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                if request.stream:
                    # Stream response
                    async with client.stream(
                        "POST",
                        url,
                        headers=headers,
                        json=request.dict(exclude_none=True)
                    ) as response:
                        response.raise_for_status()
                        async for line in response.aiter_lines():
                            if line.strip():
                                if line.startswith("data: "):
                                    data_str = line[6:]  # Remove "data: " prefix
                                    if data_str.strip() == "[DONE]":
                                        break
                                    try:
                                        data = json.loads(data_str)
                                        yield data
                                    except json.JSONDecodeError as e:
                                        logger.warning("Failed to parse stream data", data=data_str, error=str(e))
                else:
                    # Non-stream response
                    response = await client.post(
                        url,
                        headers=headers,
                        json=request.dict(exclude_none=True)
                    )
                    response.raise_for_status()
                    yield response.json()

            except httpx.HTTPStatusError as e:
                logger.error("HTTP error in chat completion", status_code=e.response.status_code, response=e.response.text)
                raise
            except Exception as e:
                logger.error("Error in chat completion", error=str(e))
                raise

    async def get_models(self) -> ModelsResponse:
        """Get available models from CodeFree API."""
        url = f"{self.base_url}/api/aebackend/openmodel/v1/models"
        headers = self._get_headers()

        logger.info("Getting models list", url=url)

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.get(url, headers=headers)
                response.raise_for_status()
                return ModelsResponse(**response.json())
            except httpx.HTTPStatusError as e:
                print(f"{e.response.text}")
                logger.error("HTTP error getting models", status_code=e.response.status_code, response=e.response.text)
                raise
            except Exception as e:
                print(f"{str(e)}")
                logger.error("Error getting models", error=str(e))
                raise

    async def get_knowledge_bases(self) -> KnowledgeBaseListResponse:
        """Get knowledge base list from CodeFree API."""
        url = f"{self.base_url}/api/smartassistbackend/aiknowledge-open/v1/kb-list"
        headers = self._get_headers()

        logger.info("Getting knowledge bases list", url=url)

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.get(url, headers=headers)
                response.raise_for_status()
                return KnowledgeBaseListResponse(**response.json())
            except httpx.HTTPStatusError as e:
                logger.error("HTTP error getting knowledge bases", status_code=e.response.status_code, response=e.response.text)
                raise
            except Exception as e:

                logger.error("Error getting knowledge bases", error=str(e))
                raise

    async def search_knowledge_base(self, kb_id: int, query: str) -> KnowledgeBaseSearchResponse:
        """Search in knowledge base."""
        url = f"{self.base_url}/api/smartassistbackend/aiknowledge-open/v1/doc-search"
        headers = self._get_headers()
        data = {"kbId": kb_id, "doc": query}

        logger.info("Searching knowledge base", url=url, kb_id=kb_id, query=query[:100])

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                response = await client.post(url, headers=headers, json=data)
                response.raise_for_status()
                return KnowledgeBaseSearchResponse(**response.json())
            except httpx.HTTPStatusError as e:
                logger.error("HTTP error searching knowledge base", status_code=e.response.status_code, response=e.response.text)
                raise
            except Exception as e:
                logger.error("Error searching knowledge base", error=str(e))
                raise

    async def knowledge_base_chat(
        self,
        request: KnowledgeBaseChatRequest
    ) -> AsyncGenerator[Dict[str, Any], None]:
        """Chat with knowledge base."""
        url = f"{self.base_url}/api/smartassistbackend/chat/open_universal_chat"
        headers = self._get_headers()

        logger.info("Knowledge base chat", url=url, kb_id=request.knowledge_base_id)

        async with httpx.AsyncClient(timeout=self.timeout) as client:
            try:
                # Knowledge base chat is always streaming
                async with client.stream(
                    "POST",
                    url,
                    headers=headers,
                    json=request.dict(exclude_none=True)
                ) as response:
                    response.raise_for_status()
                    async for line in response.aiter_lines():
                        if line.strip():
                            if line.startswith("data: "):
                                data_str = line[6:]  # Remove "data: " prefix
                                if data_str.strip() == "[DONE]":
                                    break
                                try:
                                    data = json.loads(data_str)
                                    yield data
                                except json.JSONDecodeError as e:
                                    logger.warning("Failed to parse KB chat stream data", data=data_str, error=str(e))

            except httpx.HTTPStatusError as e:
                logger.error("HTTP error in KB chat", status_code=e.response.status_code, response=e.response.text)
                raise
            except Exception as e:
                logger.error("Error in KB chat", error=str(e))
                raise


# Global client instance
client = CodeFreeClient()
