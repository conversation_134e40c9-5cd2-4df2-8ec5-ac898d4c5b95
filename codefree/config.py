"""Configuration management for Code<PERSON>ree to OpenAI API converter."""

import os
from typing import Optional
from pydantic_settings import BaseSettings
from pydantic import Field


class CodeFreeConfig(BaseSettings):
    """CodeFree API configuration."""

    # CodeFree API settings
    CODEFREE_BASE_URL: str = Field(default="https://www.srdcloud.cn")
    CODEFREE_ACCOUNT: str
    CODEFREE_API_KEY: str
    CODEFREE_AUTHORIZATION: str
    CODEFREE_BUSINESS_KEY: Optional[str] = None

    # Service settings
    HOST: str = Field(default="0.0.0.0")
    PORT: int = Field(default=8000)
    LOG_LEVEL: str = Field(default="INFO")

    # OpenAI compatibility settings
    DEFAULT_MODEL: str = Field(default="gpt-3.5-turbo")
    MAX_TOKENS_DEFAULT: int = Field(default=1000)

    class Config:
        env_file = ".env"
        env_file_encoding = "utf-8"

    @property
    def account(self) -> str:
        return self.CODEFREE_ACCOUNT
    
    @property
    def api_key(self) -> str:
        return self.CODEFREE_API_KEY

    @property
    def authorization(self) -> str:
        return self.CODEFREE_AUTHORIZATION

    @property
    def business_key(self) -> Optional[str]:
        return self.CODEFREE_BUSINESS_KEY


# Global config instance
config = CodeFreeConfig()
